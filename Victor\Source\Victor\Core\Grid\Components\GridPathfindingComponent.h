// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "GridPathfindingComponent.generated.h"


struct FTileDataStruct;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class VICTOR_API UGridPathfindingComponent : public UActorComponent
{
	GENERATED_BODY()

	//Functions
public:
	// Sets default values for this component's properties
	UGridPathfindingComponent();

private:
	TArray<FIntPoint> FindSquareNeighbors(FIntPoint Index);

	TArray<FIntPoint> FindHexagonNeighbors(FIntPoint Index);

	TArray<FIntPoint> FindTriangleNeighbors(FIntPoint Index);

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	TArray<FIntPoint> GetTileNeighborList(EGridShapeEnum Shape, FIntPoint Index);

	TArray<FIntPoint> GetTileValidNeighborList(const TMap<FIntPoint, FTileDataStruct>& TileDataMap, EGridShapeEnum Shape, FVector TileSize, FIntPoint Index);
};
