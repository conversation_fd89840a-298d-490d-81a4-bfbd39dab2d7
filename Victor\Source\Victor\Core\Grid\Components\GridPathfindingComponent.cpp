// Fill out your copyright notice in the Description page of Project Settings.


#include "GridPathfindingComponent.h"

#include "Victor/Core/Grid/Utilities/GridFunctionLibrary.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Victor/Core/Grid/Utilities/TileTypeEnum.h"

// Sets default values for this component's properties
UGridPathfindingComponent::UGridPathfindingComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = false;

	// ...
}


// Called when the game starts
void UGridPathfindingComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
}

TArray<FIntPoint> UGridPathfindingComponent::FindSquareNeighbors(const FIntPoint Index)
{
	TArray<FIntPoint> Ret;

	Ret.Add(FIntPoint(Index.X + 1, Index.Y));
	Ret.Add(FIntPoint(Index.X, Index.Y + 1));
	Ret.Add(FIntPoint(Index.X - 1, Index.Y));
	Ret.Add(FIntPoint(Index.X, Index.Y - 1));

	//TODO Corners?
	Ret.Add(FIntPoint(Index.X + 1, Index.Y + 1));
	Ret.Add(FIntPoint(Index.X - 1, Index.Y + 1));
	Ret.Add(FIntPoint(Index.X + 1, Index.Y - 1));
	Ret.Add(FIntPoint(Index.X - 1, Index.Y - 1));

	//TODO How do we validate the index?

	return Ret;
}

TArray<FIntPoint> UGridPathfindingComponent::FindHexagonNeighbors(const FIntPoint Index)
{
	TArray<FIntPoint> Ret;

	Ret.Add(FIntPoint(Index.X + 1, Index.Y + 1));
	Ret.Add(FIntPoint(Index.X, Index.Y + 2));
	Ret.Add(FIntPoint(Index.X - 1, Index.Y + 1));
	Ret.Add(FIntPoint(Index.X - 1, Index.Y - 1));
	Ret.Add(FIntPoint(Index.X, Index.Y - 2));
	Ret.Add(FIntPoint(Index.X + 1, Index.Y - 1));

	return Ret;
}

TArray<FIntPoint> UGridPathfindingComponent::FindTriangleNeighbors(FIntPoint Index)
{
	TArray<FIntPoint> Ret;

	if (Index.X % 2 != Index.Y % 2)
	{
		Ret.Add(FIntPoint(Index.X, Index.Y + 1));
		Ret.Add(FIntPoint(Index.X, Index.Y - 1));
		Ret.Add(FIntPoint(Index.X + 1, Index.Y));
	}
	else
	{
		Ret.Add(FIntPoint(Index.X, Index.Y + 1));
		Ret.Add(FIntPoint(Index.X, Index.Y - 1));
		Ret.Add(FIntPoint(Index.X - 1, Index.Y));
	}

	return Ret;
}

TArray<FIntPoint> UGridPathfindingComponent::GetTileNeighborList(const EGridShapeEnum Shape, const FIntPoint Index)
{
	TArray<FIntPoint> Ret;

	switch (Shape)
	{
	case EGridShapeEnum::Square:
		Ret = FindSquareNeighbors(Index);
		break;
	case EGridShapeEnum::Hexagon:
		Ret = FindHexagonNeighbors(Index);
		break;
	case EGridShapeEnum::Triangle:
		Ret = FindTriangleNeighbors(Index);
	default: ;
	}

	return Ret;
}

TArray<FIntPoint> UGridPathfindingComponent::GetTileValidNeighborList(const TMap<FIntPoint, FTileDataStruct>& TileDataMap, EGridShapeEnum Shape, FVector TileSize, FIntPoint Index)
{
	// Debug: Check the map size and contents
	UE_LOG(LogTemp, Warning, TEXT("GetTileValidNeighborList: TileDataMap size = %d"), TileDataMap.Num());
	UE_LOG(LogTemp, Warning, TEXT("GetTileValidNeighborList: Looking for Index (%d, %d)"), Index.X, Index.Y);

	// Debug: Print first few keys in the map
	TArray<FIntPoint> Keys;
	TileDataMap.GetKeys(Keys);
	for (int32 i = 0; i < FMath::Min(5, Keys.Num()); i++)
	{
		UE_LOG(LogTemp, Warning, TEXT("GetTileValidNeighborList: Map contains key (%d, %d)"), Keys[i].X, Keys[i].Y);
	}

	const FTileDataStruct* MainTileData = TileDataMap.Find(Index);

	if (!MainTileData)
	{
		UE_LOG(LogTemp, Error, TEXT("GetTileValidNeighborList: MainTileData is NULL for Index (%d, %d)"), Index.X, Index.Y);
		return TArray<FIntPoint>();
	}

	const FTransform MainTransform = MainTileData->Transform;
	const FVector MainLocation = MainTransform.GetLocation();

	TArray<FIntPoint> NeighborList = GetTileNeighborList(Shape, Index);

	TArray<FIntPoint> Ret;
	for (FIntPoint TileIndex : NeighborList)
	{
		const FTileDataStruct* NeighborTileData = TileDataMap.Find(TileIndex);
		const ETileTypeEnum NeighborTileType = NeighborTileData->TileType;

		const bool IsWalkable = UGridFunctionLibrary::IsTileWalkable(NeighborTileType);
		if (IsWalkable)
		{
			const FTransform NeighborTransform = NeighborTileData->Transform;
			const FVector NeighborLocation = NeighborTransform.GetLocation();
			const float HeighDifference = FMath::Abs(MainLocation.Z - NeighborLocation.Z);

			if (HeighDifference <= (TileSize.Z + 10))
			{
				Ret.Add(TileIndex);
			}
		}
	}

	return Ret;
}
